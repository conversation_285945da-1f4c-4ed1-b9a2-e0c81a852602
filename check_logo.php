<?php
// <PERSON>ript to check logo generation without outputting image
echo "<h2>Logo Generation Check</h2>";

// Domain to display
$domain = $_SERVER['HTTP_HOST'];
echo "<p><strong>Domain:</strong> $domain</p>";

// Font settings
$fontSize = 24;
$font = __DIR__ . '/Roboto.ttf';

echo "<p><strong>Font file exists:</strong> " . (file_exists($font) ? "✅ Yes" : "❌ No") . "</p>";
echo "<p><strong>Font path:</strong> $font</p>";

// Calculate image dimensions based on text size
$padding = 10; // Padding from edges
$fontSize = 24;

// Calculate text dimensions to determine proper image size
if (file_exists($font)) {
    $textBox = imagettfbbox($fontSize, 0, $font, $domain);
    $textWidth = $textBox[2] - $textBox[0];
    $textHeight = $textBox[1] - $textBox[7];
} else {
    $textWidth = strlen($domain) * 12;
    $textHeight = 15;
}

$width = $textWidth + ($padding * 2);
$height = $textHeight + ($padding * 2);

echo "<p><strong>Calculated dimensions:</strong> {$width}px × {$height}px</p>";
echo "<p><strong>Text dimensions:</strong> {$textWidth}px × {$textHeight}px</p>";
echo "<p><strong>Padding:</strong> {$padding}px từ tất cả các bên</p>";

// Check GD extension
if (extension_loaded('gd')) {
    echo "<p><strong>GD Extension:</strong> ✅ Available</p>";
    $gdInfo = gd_info();
    echo "<p><strong>GD Version:</strong> " . $gdInfo['GD Version'] . "</p>";
    echo "<p><strong>PNG Support:</strong> " . ($gdInfo['PNG Support'] ? "✅ Yes" : "❌ No") . "</p>";
    echo "<p><strong>FreeType Support:</strong> " . ($gdInfo['FreeType Support'] ? "✅ Yes" : "❌ No") . "</p>";
} else {
    echo "<p><strong>GD Extension:</strong> ❌ Not available</p>";
}

// Test image creation (without output)
try {
    $image = imagecreatetruecolor($width, $height);
    if ($image) {
        echo "<p><strong>Image creation:</strong> ✅ Success</p>";
        
        // Test color allocation
        $textColor = imagecolorallocate($image, 34, 34, 34); // #222
        if ($textColor !== false) {
            echo "<p><strong>Color allocation:</strong> ✅ Success (#222 = rgb(34,34,34))</p>";
        } else {
            echo "<p><strong>Color allocation:</strong> ❌ Failed</p>";
        }
        
        // Test font rendering if font exists
        if (file_exists($font)) {
            $textBox = imagettfbbox($fontSize, 0, $font, $domain);
            if ($textBox !== false) {
                echo "<p><strong>Font rendering test:</strong> ✅ Success</p>";
                $textWidth = $textBox[2] - $textBox[0];
                $textHeight = $textBox[1] - $textBox[7];
                echo "<p><strong>Text dimensions:</strong> {$textWidth}px × {$textHeight}px</p>";
            } else {
                echo "<p><strong>Font rendering test:</strong> ❌ Failed</p>";
            }
        }
        
        imagedestroy($image);
    } else {
        echo "<p><strong>Image creation:</strong> ❌ Failed</p>";
    }
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Test Logo Display:</h3>";
echo "<img src='logo.php' alt='Generated Logo' style='border: 1px solid #ccc; background: white; padding: 10px;'>";

echo "<hr>";
echo "<h3>Logo Specifications Met:</h3>";
echo "<ul>";
echo "<li>✅ Height: Auto (text height + 20px padding)</li>";
echo "<li>✅ Width: Auto (text width + 20px padding)</li>";
echo "<li>✅ Color: #222 (rgb(34,34,34))</li>";
echo "<li>✅ Font: Roboto</li>";
echo "<li>✅ Content: Current domain ($domain)</li>";
echo "</ul>";
?>
