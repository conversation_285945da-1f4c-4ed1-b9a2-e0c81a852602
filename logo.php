<?php
// Set content type header
header('Content-Type: image/png');

// Domain to display
$domain = $_SERVER['HTTP_HOST'];

// Font settings
$fontSize = 24; // Increased font size for 150px height
$font = __DIR__ . '/Roboto.ttf';

// Calculate image dimensions based on domain length and text size
$padding = 10; // Padding from edges
$fontSize = 24;

// First, calculate text dimensions to determine proper image size
if (file_exists($font)) {
    $textBox = imagettfbbox($fontSize, 0, $font, $domain);
    $textWidth = $textBox[2] - $textBox[0];
    $textHeight = $textBox[1] - $textBox[7];
} else {
    // Fallback for built-in font
    $textWidth = strlen($domain) * 12; // Approximate
    $textHeight = 15; // Approximate height for built-in font
}

// Calculate image dimensions with 10px padding on all sides
$width = $textWidth + ($padding * 2);
$height = $textHeight + ($padding * 2);

// Create image with transparent background
$image = imagecreatetruecolor($width, $height);
imagealphablending($image, false);
imagesavealpha($image, true);

// Allocate colors
$transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
$textColor = imagecolorallocate($image, 34, 34, 34); // #222

// Fill background with transparent color
imagefill($image, 0, 0, $transparent);

// If font file doesn't exist, use default font
if (!file_exists($font)) {
    // Use built-in font as fallback with 10px padding
    imagestring($image, 5, $padding, $padding, $domain, $textColor);
} else {
    // Position text with 10px padding from edges
    $x = $padding;
    $y = $padding + $textHeight; // Baseline position for TTF text

    // Add text to image
    imagettftext($image, $fontSize, 0, $x, $y, $textColor, $font, $domain);
}

// Output the image
imagepng($image);

// Free memory
imagedestroy($image);
