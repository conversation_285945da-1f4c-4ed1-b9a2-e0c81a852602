<?php
// Set content type header
header('Content-Type: image/png');

// Domain to display
$domain = $_SERVER['HTTP_HOST'];

// Font settings
$fontSize = 24; // Increased font size for 150px height
$font = __DIR__ . '/Roboto.ttf';

// Calculate image dimensions based on domain length
$padding = 20; // Total padding (10px on each side)
$baseWidth = 100; // Minimum width
$charWidth = 15;  // Approximate width per character for larger font
$width = max($baseWidth, strlen($domain) * $charWidth + $padding); // Add minimal padding
$height = 150; // Set height to 150px as requested

// Create image with transparent background
$image = imagecreatetruecolor($width, $height);
imagealphablending($image, false);
imagesavealpha($image, true);

// Allocate colors
$transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
$textColor = imagecolorallocate($image, 34, 34, 34); // #222

// Fill background with transparent color
imagefill($image, 0, 0, $transparent);

// If font file doesn't exist, use default font
if (!file_exists($font)) {
    // Use built-in font as fallback
    imagestring($image, 5, 10, ($height - 20) / 2, $domain, $textColor);
} else {
    // Calculate text position to center it
    $textBox = imagettfbbox($fontSize, 0, $font, $domain);
    $textWidth = $textBox[2] - $textBox[0];
    $textHeight = $textBox[1] - $textBox[7];
    $x = ($width - $textWidth) / 2;
    $y = ($height + $textHeight) / 2;
    
    // Add text to image
    imagettftext($image, $fontSize, 0, $x, $y, $textColor, $font, $domain);
}

// Output the image
imagepng($image);

// Free memory
imagedestroy($image);
