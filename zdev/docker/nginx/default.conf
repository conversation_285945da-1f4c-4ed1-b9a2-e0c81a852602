server {
    listen 80;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www;

    if ($request_filename ~ /robots.txt$){
        rewrite ^(.*)$ /robots.php?action=$http_host break;
    }
        
    rewrite ^/install/check\.rewrite$ /install/rewrite.php break;
    rewrite ^/assets/images/logo\.png$ /logo.php break;
    rewrite ^/(.*?)sitemap\.xml$ /index.php?nv=SitemapIndex break;
    rewrite "^/(.*?)sitemap\-([a-z]{2})\.xml$" /index.php?language=$2&nv=SitemapIndex break;
    rewrite "^/(.*?)sitemap\-([a-z]{2})\.([a-zA-Z0-9-]+)\.xml$" /index.php?language=$2&nv=$3&op=sitemap break;
    if (!-e $request_filename){
        rewrite (.*)(\/|\.html)$ /index.php;
        rewrite /(.*)tag/(.*)$ /index.php;
    }

    rewrite ^/seek\/q\=([^?]+)$ /index.php?nv=seek&q=$1 break;
    rewrite ^/search\/q\=([^?]+)$ /index.php?nv=news&op=search&q=$1 break;
    rewrite ^/([a-zA-Z0-9\-]+)\/search\/q\=([^?]+)$ /index.php?nv=$1&op=search&q=$2 break; 
    rewrite ^/([a-zA-Z0-9-\/]+)\/([a-zA-Z0-9-]+)$ /$1/$2/ break; 
    rewrite ^/([a-zA-Z0-9-]+)$ /$1/ break;        
    location ~ ^/admin/([a-z0-9]+)/(.*)$ {
        deny all;
    }
    location ~ ^/(config|includes|vendor)/(.*)$ {
        deny all;
    }
    location ~ ^/data/(cache|ip|ip6|logs)/(.*)$ {
        deny all;
    }
    location ~ ^/(files|uploads|themes)/(.*).(php|ini|tpl|php3|php4|php5|phtml|shtml|inc|pl|py|jsp|sh|cgi)$ {
        deny all;
    }

    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass dgtgrcloner_app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }
}
